#!/usr/bin/env python3
"""
Тестовый скрипт для проверки статистики по предмету для менеджера
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.getcwd())

async def test_manager_subject():
    """Тестирование статистики по предмету для менеджера"""
    try:
        from database import init_database, SubjectRepository
        from common.analytics.handlers import show_subject_analytics
        from common.analytics.keyboards import get_subject_microtopics_kb
        
        print('🔧 Инициализация базы данных...')
        await init_database()
        
        print('📚 Получение списка предметов...')
        subjects = await SubjectRepository.get_all()
        
        if not subjects:
            print('❌ Предметы не найдены в базе данных')
            return
            
        # Тестируем клавиатуру
        test_subject = next((s for s in subjects if s.name == "Python"), subjects[0])
        print(f'🎯 Тестирование клавиатуры для предмета: {test_subject.name}')
        
        keyboard = get_subject_microtopics_kb(test_subject.id)
        print(f'✅ Клавиатура создана с {len(keyboard.inline_keyboard)} кнопками')
        
        for row in keyboard.inline_keyboard:
            for button in row:
                print(f'   - {button.text} -> {button.callback_data}')
        
    except Exception as e:
        print(f'❌ Ошибка при тестировании: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_manager_subject())
