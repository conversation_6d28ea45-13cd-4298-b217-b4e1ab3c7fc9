#!/usr/bin/env python3
"""
Тестовый скрипт для проверки статистики по микротемам предмета
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.getcwd())

async def test_subject_microtopics():
    """Тестирование статистики по микротемам предмета"""
    try:
        from database import init_database, SubjectRepository
        from common.statistics import get_subject_microtopics_detailed, get_subject_microtopics_summary
        
        print('🔧 Инициализация базы данных...')
        await init_database()
        
        print('📚 Получение списка предметов...')
        subjects = await SubjectRepository.get_all()
        
        if not subjects:
            print('❌ Предметы не найдены в базе данных')
            return
            
        print(f'✅ Найдено предметов: {len(subjects)}')
        for i, subject in enumerate(subjects[:3], 1):  # Показываем первые 3
            print(f'{i}. {subject.name} (ID: {subject.id})')
        
        # Тестируем статистику по предмету Python (ID: 6)
        if subjects:
            test_subject = next((s for s in subjects if s.name == "Python"), subjects[0])
            print(f'\n📊 Тестирование статистики по предмету: {test_subject.name}')
            
            # Тестируем детальную статистику
            print(f'\n📈 Детальная статистика по микротемам:')
            detailed_stats = await get_subject_microtopics_detailed(test_subject.id)
            print(detailed_stats)
            
            # Тестируем сводную статистику
            print(f'\n🟢🔴 Сводка по сильным и слабым темам:')
            summary_stats = await get_subject_microtopics_summary(test_subject.id)
            print(summary_stats)
        
    except Exception as e:
        print(f'❌ Ошибка при тестировании: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_subject_microtopics())
