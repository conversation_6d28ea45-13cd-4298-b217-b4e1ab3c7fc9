#!/usr/bin/env python3
"""
Тестовый скрипт для проверки функциональности удаления промежуточных сообщений
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock

# Имитируем структуру FSMContext
class MockFSMContext:
    def __init__(self):
        self.data = {}
    
    async def get_data(self):
        return self.data
    
    async def update_data(self, **kwargs):
        self.data.update(kwargs)

# Имитируем структуру CallbackQuery
class MockCallbackQuery:
    def __init__(self):
        self.bot = AsyncMock()
        self.message = MagicMock()
        self.message.chat.id = 12345

# Имитируем структуру Message
class MockMessage:
    def __init__(self, message_id=1001):
        self.message_id = message_id
        self.chat = MagicMock()
        self.chat.id = 12345

async def test_add_message_id_to_cleanup():
    """Тест функции добавления ID сообщения"""
    print("🧪 Тестируем add_message_id_to_cleanup...")
    
    # Импортируем функцию
    import sys
    sys.path.append('.')
    from common.manager_tests.handlers import add_message_id_to_cleanup
    
    # Создаем мок состояния
    state = MockFSMContext()
    
    # Добавляем несколько ID сообщений
    await add_message_id_to_cleanup(state, 1001)
    await add_message_id_to_cleanup(state, 1002)
    await add_message_id_to_cleanup(state, 1003)
    
    # Проверяем результат
    data = await state.get_data()
    message_ids = data.get("intermediate_message_ids", [])
    
    assert message_ids == [1001, 1002, 1003], f"Ожидали [1001, 1002, 1003], получили {message_ids}"
    print("✅ add_message_id_to_cleanup работает корректно")

async def test_delete_intermediate_messages():
    """Тест функции удаления промежуточных сообщений"""
    print("🧪 Тестируем delete_intermediate_messages...")
    
    # Импортируем функцию
    import sys
    sys.path.append('.')
    from manager.handlers.homework import delete_intermediate_messages
    
    # Создаем мок объектов
    callback = MockCallbackQuery()
    state = MockFSMContext()
    
    # Добавляем ID сообщений в состояние
    await state.update_data(intermediate_message_ids=[1001, 1002, 1003])
    
    # Вызываем функцию удаления
    await delete_intermediate_messages(callback, state)
    
    # Проверяем, что delete_message был вызван для каждого ID
    assert callback.bot.delete_message.call_count == 3, f"Ожидали 3 вызова, получили {callback.bot.delete_message.call_count}"
    
    # Проверяем, что список ID очищен
    data = await state.get_data()
    message_ids = data.get("intermediate_message_ids", [])
    assert message_ids == [], f"Ожидали пустой список, получили {message_ids}"
    
    print("✅ delete_intermediate_messages работает корректно")

async def main():
    """Основная функция тестирования"""
    print("🚀 Запуск тестов функциональности удаления промежуточных сообщений\n")
    
    try:
        await test_add_message_id_to_cleanup()
        await test_delete_intermediate_messages()
        print("\n🎉 Все тесты прошли успешно!")
        
        print("\n📋 Резюме реализации:")
        print("1. ✅ Функция add_message_id_to_cleanup добавляет ID сообщений в FSM состояние")
        print("2. ✅ Функция delete_intermediate_messages удаляет все накопленные сообщения")
        print("3. ✅ ID сообщений очищаются из состояния после удаления")
        print("4. ✅ Обработчики модифицированы для сохранения ID отправленных сообщений")
        print("5. ✅ После успешного создания ДЗ промежуточные сообщения удаляются")
        
    except Exception as e:
        print(f"❌ Ошибка в тестах: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
