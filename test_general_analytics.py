#!/usr/bin/env python3
"""
Тестовый скрипт для проверки общей статистики
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.getcwd())

async def test_general_analytics():
    """Тестирование общей статистики"""
    try:
        from database import init_database
        from common.statistics import get_general_microtopics_detailed, get_general_microtopics_summary, get_general_stats
        
        print('🔧 Инициализация базы данных...')
        await init_database()
        
        print('📊 Тестирование общей статистики...')
        general_data = await get_general_stats()
        print(f'✅ Общие данные получены: {general_data["total_students"]} студентов, {general_data["total_groups"]} групп')
        
        print('\n📈 Тестирование детальной статистики по микротемам...')
        detailed_stats = await get_general_microtopics_detailed()
        print(detailed_stats[:500] + "..." if len(detailed_stats) > 500 else detailed_stats)
        
        print('\n🟢🔴 Тестирование сводки по сильным и слабым темам...')
        summary_stats = await get_general_microtopics_summary()
        print(summary_stats[:500] + "..." if len(summary_stats) > 500 else summary_stats)
        
    except Exception as e:
        print(f'❌ Ошибка при тестировании: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_general_analytics())
