#!/usr/bin/env python3
"""
Тестовый скрипт для проверки статистики по предмету
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.getcwd())

async def test_subject_analytics():
    """Тестирование статистики по предмету"""
    try:
        from database import init_database, SubjectRepository
        from common.statistics import get_subject_stats, format_subject_stats, get_general_stats, format_general_stats
        
        print('🔧 Инициализация базы данных...')
        await init_database()
        
        print('📚 Получение списка предметов...')
        subjects = await SubjectRepository.get_all()
        
        if not subjects:
            print('❌ Предметы не найдены в базе данных')
            return
            
        print(f'✅ Найдено предметов: {len(subjects)}')
        for i, subject in enumerate(subjects[:3], 1):  # Показываем первые 3
            print(f'{i}. {subject.name} (ID: {subject.id})')
        
        # Тестируем статистику по первому предмету
        if subjects:
            test_subject = subjects[0]
            print(f'\n📊 Тестирование статистики по предмету: {test_subject.name}')
            
            # Получаем статистику
            subject_data = await get_subject_stats(str(test_subject.id))
            print(f'✅ Данные получены: {len(subject_data.get("groups", []))} групп')
            
            # Форматируем статистику
            formatted_text = format_subject_stats(subject_data)
            print(f'\n📋 Отформатированная статистика:')
            print(formatted_text)
        
        # Тестируем общую статистику
        print(f'\n📊 Тестирование общей статистики...')
        general_data = await get_general_stats()
        print(f'✅ Общие данные получены')
        
        formatted_general = format_general_stats(general_data)
        print(f'\n📋 Общая статистика:')
        print(formatted_general)
        
    except Exception as e:
        print(f'❌ Ошибка при тестировании: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_subject_analytics())
